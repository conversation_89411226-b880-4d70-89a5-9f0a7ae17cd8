# Tailwind迁移 - 第一阶段PRD文档

**文档版本**: v1.0  
**创建日期**: 2025-01-24  
**阶段名称**: 准备工作阶段  
**预估工时**: 4小时 (0.5个工作日)  
**负责人**: [已分配]  
**状态**: 进行中

---

## 📋 阶段概述

### 目标
完成Tailwind CSS移除的前期准备工作，建立基于Ant Design的样式工具体系，为后续组件迁移奠定基础。

### 范围
- 移除Tailwind CSS相关配置和依赖
- 创建Ant Design样式增强工具函数
- 建立CSS Modules文件结构
- 配置构建工具支持新的样式方案

### 成功标准
- [ ] Tailwind CSS完全移除，项目能正常构建
- [/] 样式工具函数创建完成并通过测试
- [ ] CSS Modules配置生效
- [ ] 构建工具配置更新完成

---

## 🎯 详细任务清单

### 任务1: 移除Tailwind CSS配置和依赖
**预估工时**: 1小时  
**优先级**: P0 (最高)  
**依赖**: 无  
**状态**: 未开始

#### 子任务清单
- [ ] **1.1** 移除package.json中的Tailwind相关依赖
  - 移除 `tailwindcss`
  - 移除 `autoprefixer`
  - 移除 `postcss`
- [ ] **1.2** 删除Tailwind配置文件
  - 删除 `tailwind.config.js`
  - 删除 `postcss.config.js`
- [ ] **1.3** 更新全局样式文件
  - 修改 `src/app/globals.css`
  - 移除 `@tailwind` 指令
- [ ] **1.4** 验证项目构建
  - 运行 `npm run build`
  - 确保无Tailwind相关错误

#### 技术实施细节
```bash
# 移除依赖
npm uninstall tailwindcss autoprefixer postcss

# 删除配置文件
rm tailwind.config.js
rm postcss.config.js
```

#### 验收标准
- 项目能够正常构建，无Tailwind相关错误
- package.json中不包含Tailwind相关依赖
- 配置文件已完全删除

#### 风险评估
- **风险**: 可能存在遗漏的Tailwind类名导致样式丢失
- **缓解**: 在后续任务中逐步替换，暂时保留原有样式

---

### 任务2: 创建Ant Design样式增强工具
**预估工时**: 1.5小时  
**优先级**: P0 (最高)  
**依赖**: 任务1完成  
**状态**: 部分完成

#### 子任务清单
- [x] **2.1** 创建样式工具函数文件
  - 创建 `src/utils/styles/antdHelpers.ts`
  - 实现间距、阴影、圆角等工具函数
- [ ] **2.2** 创建主题配置文件
  - 创建 `src/styles/theme.ts`
  - 定义统一的设计token
- [ ] **2.3** 创建样式常量文件
  - 创建 `src/styles/constants.ts`
  - 定义常用的样式常量
- [ ] **2.4** 编写工具函数测试
  - 创建 `src/utils/styles/__tests__/antdHelpers.test.ts`
  - 覆盖主要工具函数

#### 技术实施细节
```typescript
// src/utils/styles/antdHelpers.ts
export const styleHelpers = {
  spacing: {
    xs: 4, sm: 8, md: 16, lg: 24, xl: 32
  },
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
  },
  borderRadius: {
    sm: 4, md: 8, lg: 12
  },
  // 生成内联样式的工具函数
  createSpacing: (size: keyof typeof styleHelpers.spacing) => ({
    padding: styleHelpers.spacing[size]
  }),
  createShadow: (size: keyof typeof styleHelpers.shadows) => ({
    boxShadow: styleHelpers.shadows[size]
  })
}
```

#### 验收标准
- 样式工具函数文件创建完成
- 所有工具函数通过单元测试
- 主题配置能够正确应用到Ant Design组件
- TypeScript类型检查通过

#### 风险评估
- **风险**: 工具函数设计不够灵活，无法满足所有场景
- **缓解**: 采用渐进式设计，后续根据实际使用情况调整

---

### 任务3: 建立CSS Modules文件结构
**预估工时**: 1小时  
**优先级**: P1 (高)  
**依赖**: 任务2完成  
**状态**: 未开始

#### 子任务清单
- [ ] **3.1** 创建CSS Modules目录结构
  - 创建 `src/styles/modules/` 目录
  - 创建组件级样式目录结构
- [ ] **3.2** 创建通用CSS Modules文件
  - 创建 `src/styles/modules/common.module.css`
  - 定义通用的CSS类
- [ ] **3.3** 创建布局相关CSS Modules
  - 创建 `src/styles/modules/layout.module.css`
  - 定义布局相关样式
- [ ] **3.4** 创建动画CSS Modules
  - 创建 `src/styles/modules/animations.module.css`
  - 定义常用动画效果

#### 技术实施细节
```css
/* src/styles/modules/common.module.css */
.card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.cardHover:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.flexCenter {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flexBetween {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
```

#### 验收标准
- CSS Modules目录结构创建完成
- 通用样式类定义完整
- 样式文件能够正确导入和使用
- 样式类命名规范统一

#### 风险评估
- **风险**: CSS Modules类名冲突
- **缓解**: 采用BEM命名规范，确保类名唯一性

---

### 任务4: 配置构建工具支持新的样式方案
**预估工时**: 0.5小时  
**优先级**: P1 (高)  
**依赖**: 任务3完成  
**状态**: 未开始

#### 子任务清单
- [ ] **4.1** 更新Next.js配置
  - 修改 `next.config.js`
  - 配置CSS Modules支持
- [ ] **4.2** 创建TypeScript类型声明
  - 创建 `src/types/css-modules.d.ts`
  - 定义CSS Modules类型
- [ ] **4.3** 更新TypeScript配置
  - 修改 `tsconfig.json`
  - 添加路径别名

#### 技术实施细节
```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  transpilePackages: ['antd'],
  compiler: {
    styledComponents: true,
  },
  reactStrictMode: false,
  
  // 启用CSS Modules支持
  cssModules: true,
  cssLoaderOptions: {
    modules: {
      localIdentName: '[name]__[local]___[hash:base64:5]',
    },
  },
}

module.exports = nextConfig
```

```typescript
// src/types/css-modules.d.ts
declare module '*.module.css' {
  const classes: { [key: string]: string }
  export default classes
}

declare module '*.module.scss' {
  const classes: { [key: string]: string }
  export default classes
}
```

#### 验收标准
- Next.js配置更新完成
- CSS Modules能够正确加载和使用
- TypeScript类型检查通过
- 构建流程无错误

#### 风险评估
- **风险**: 构建配置冲突导致项目无法启动
- **缓解**: 分步骤验证配置，及时回滚问题配置

---

## 📊 任务依赖关系图

```
任务1: 移除Tailwind配置
    ↓
任务2: 创建样式工具函数
    ↓
任务3: 建立CSS Modules结构
    ↓
任务4: 配置构建工具
```

## ⏰ 时间安排

| 任务 | 开始时间 | 结束时间 | 工时 |
|------|----------|----------|------|
| 任务1 | 09:00 | 10:00 | 1h |
| 任务2 | 10:00 | 11:30 | 1.5h |
| 任务3 | 11:30 | 12:30 | 1h |
| 任务4 | 14:00 | 14:30 | 0.5h |

## 🔍 质量检查清单

### 代码质量
- [ ] 所有新增文件通过ESLint检查
- [ ] TypeScript类型检查无错误
- [ ] 单元测试覆盖率 ≥ 80%

### 功能验证
- [x] 项目能够正常启动
- [/] 样式工具函数功能正常
- [ ] CSS Modules能够正确应用

### 文档更新
- [ ] 更新README.md中的样式使用说明
- [ ] 创建样式工具函数使用文档
- [ ] 更新开发规范文档

## � 交付物清单

### 配置文件更新
- [ ] `package.json` - 移除Tailwind依赖
- [ ] `next.config.js` - 配置CSS Modules支持
- [ ] `tsconfig.json` - 添加路径别名和类型支持
- [ ] `src/app/globals.css` - 移除Tailwind指令

### 新增工具文件
- [x] `src/utils/styles/antdHelpers.ts` - 样式工具函数
- [ ] `src/styles/theme.ts` - Ant Design主题配置
- [ ] `src/styles/constants.ts` - 样式常量定义
- [ ] `src/types/css-modules.d.ts` - CSS Modules类型声明

### CSS Modules文件
- [ ] `src/styles/modules/common.module.css` - 通用样式类
- [ ] `src/styles/modules/layout.module.css` - 布局相关样式
- [ ] `src/styles/modules/animations.module.css` - 动画效果

### 测试文件
- [ ] `src/utils/styles/__tests__/antdHelpers.test.ts` - 工具函数单元测试

### 文档更新
- [ ] 样式工具函数使用文档
- [ ] CSS Modules使用指南
- [ ] 迁移注意事项文档

### 删除的文件
- [ ] `tailwind.config.js` - Tailwind配置文件
- [ ] `postcss.config.js` - PostCSS配置文件

## 📊 质量指标达成情况

### 代码质量指标
- [ ] ESLint检查通过率: 目标100%
- [/] TypeScript类型检查通过率: 部分通过
- [ ] 单元测试覆盖率: 目标≥80% (当前0%)

### 功能完整性指标
- [/] 样式工具函数完整性: 60% (核心函数已创建，缺少测试)
- [ ] CSS Modules类覆盖率: 0% (未创建)
- [ ] 主题配置完整性: 0% (未创建独立配置文件)

### 文档完整性指标
- [ ] API文档覆盖率: 0%
- [ ] 使用示例完整性: 0%
- [ ] 迁移指南完整性: 0%

## 🧪 验收测试结果

### 构建测试
- [x] `npm run build` 执行成功 (项目仍使用Tailwind)
- [/] TypeScript编译有部分错误
- [ ] ESLint检查待完成
- [ ] 构建产物大小待优化

### 功能测试
- [/] 样式工具函数已创建但未测试
- [ ] CSS Modules类名生成功能未实现
- [ ] 主题配置未独立创建
- [ ] 路径别名未配置

### 兼容性测试
- [x] 开发环境正常运行
- [ ] 生产环境构建待验证
- [x] 热重载功能正常
- [/] 类型提示部分工作

---

## 📝 当前状态报告

**更新日期**: 2025-01-31
**实际工时**: 约1小时 (仅完成样式工具函数)
**状态**: 进行中 (整体完成度约15%)

### 已完成的工作
- ✅ 创建了完整的样式工具函数文件 `src/utils/styles/antdHelpers.ts`
- ✅ 实现了Tailwind到内联样式的转换工具
- ✅ 定义了完整的设计token和工具函数

### 待完成的关键工作
- ❌ Tailwind CSS依赖和配置完全未移除
- ❌ CSS Modules文件结构未建立
- ❌ 构建工具配置未更新
- ❌ 缺少测试文件和文档

### 风险提示
- 🚨 项目仍完全依赖Tailwind CSS，存在样式冲突风险
- ⚠️ 文档状态与实际代码库状态严重不符
- ⚠️ 缺少基础设施支持，无法进行后续阶段迁移

### 下一步行动计划
1. 立即执行任务1：移除Tailwind CSS依赖
2. 完成CSS Modules基础设施建设
3. 更新构建工具配置
4. 补充测试文件和文档

---

## 🔍 实际状态分析 (2025-01-31)

### 代码库现状检查结果

#### ❌ 任务1: Tailwind移除状态
```bash
# package.json中仍存在的依赖
"tailwindcss": "^3.3.6"
"autoprefixer": "^10.4.16"
"postcss": "^8.4.32"

# 仍存在的配置文件
- tailwind.config.js ✓ 存在
- postcss.config.js ✓ 存在

# globals.css中仍存在的Tailwind指令
@tailwind base;
@tailwind components;
@tailwind utilities;
```

#### ✅ 任务2: 样式工具函数状态
```bash
# 已创建的文件
✓ src/utils/styles/antdHelpers.ts (195行，功能完整)

# 缺失的文件
✗ src/styles/theme.ts
✗ src/styles/constants.ts
✗ src/utils/styles/__tests__/antdHelpers.test.ts
```

#### ❌ 任务3: CSS Modules状态
```bash
# 缺失的目录和文件
✗ src/styles/ (整个目录不存在)
✗ src/styles/modules/
✗ src/styles/modules/common.module.css
✗ src/styles/modules/layout.module.css
✗ src/styles/modules/animations.module.css
```

#### ❌ 任务4: 构建配置状态
```bash
# next.config.js缺少CSS Modules配置
✗ cssModules选项未配置
✗ cssLoaderOptions未配置

# 缺失的类型声明
✗ src/types/css-modules.d.ts
```

### 整体完成度统计
- **任务1**: 0% (0/4个子任务)
- **任务2**: 25% (1/4个子任务)
- **任务3**: 0% (0/4个子任务)
- **任务4**: 0% (0/3个子任务)
- **总体**: 约15%

### 关键发现
1. **文档与现实严重脱节**: PRD标记为"已完成"，实际大部分未开始
2. **基础工作缺失**: Tailwind完全未移除，项目仍依赖Tailwind运行
3. **唯一亮点**: 样式工具函数文件创建得很完整，为后续工作奠定了基础
4. **风险较高**: 无法进行第二阶段工作，需要重新执行第一阶段大部分任务

